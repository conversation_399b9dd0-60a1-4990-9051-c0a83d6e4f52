'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ActivityFeed } from '@/components/dashboard/activity-feed'
import {
  Users,
  UserPlus,
  GraduationCap,
  DollarSign,
  TrendingUp,
  Calendar,
  CheckCircle,
  Clock,
  ArrowUpRight,
  BarChart3,
  BookOpen
} from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'
import { Button } from '@/components/ui/button'

export default function DashboardPage() {
  const { currentBranch } = useBranch()
  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 tracking-tight">Welcome to {currentBranch.name}</h1>
          <p className="text-gray-600 mt-1">Here's what's happening with your educational center today.</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="shadow-sm">
            <Calendar className="h-4 w-4 mr-2" />
            View Schedule
          </Button>
          <Button className="shadow-sm">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Total Students</CardTitle>
              <div className="h-10 w-10 rounded-full bg-blue-50 flex items-center justify-center">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-blue-700">4,247</div>
            <div className="kpi-change text-green-600 mt-2">
              <TrendingUp className="h-4 w-4" />
              <span>+12% from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">New Leads</CardTitle>
              <div className="h-10 w-10 rounded-full bg-green-50 flex items-center justify-center">
                <UserPlus className="h-5 w-5 text-green-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-green-700">127</div>
            <div className="kpi-change text-green-600 mt-2">
              <TrendingUp className="h-4 w-4" />
              <span>+8% from last week</span>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Active Groups</CardTitle>
              <div className="h-10 w-10 rounded-full bg-purple-50 flex items-center justify-center">
                <BookOpen className="h-5 w-5 text-purple-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-purple-700">89</div>
            <div className="flex items-center justify-between mt-2">
              <div className="kpi-change text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span>3 new groups</span>
              </div>
              <Button variant="ghost" size="sm" className="text-xs text-blue-600 hover:text-blue-700">
                View All
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="kpi-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <CardTitle className="kpi-label">Monthly Revenue</CardTitle>
              <div className="h-10 w-10 rounded-full bg-amber-50 flex items-center justify-center">
                <DollarSign className="h-5 w-5 text-amber-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="kpi-value text-amber-700">2.4B UZS</div>
            <div className="kpi-change text-green-600 mt-2">
              <TrendingUp className="h-4 w-4" />
              <span>+15% from last month</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Recent Leads</CardTitle>
                <CardDescription className="mt-1">Latest inquiries from potential students</CardDescription>
              </div>
              <Button variant="ghost" size="sm" className="text-sm">
                <ArrowUpRight className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-4">
              {recentLeads.map((lead, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                  <div>
                    <p className="font-medium text-gray-900">{lead.name}</p>
                    <p className="text-sm text-gray-600">{lead.course}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500 mb-1">{lead.time}</p>
                    <span className={`status-badge ${
                      lead.status === 'NEW' ? 'status-active' :
                      lead.status === 'CONTACTED' ? 'status-pending' :
                      'status-active'
                    }`}>
                      {lead.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Upcoming Classes</CardTitle>
                <CardDescription className="mt-1">Today&apos;s scheduled classes</CardDescription>
              </div>
              <Button variant="ghost" size="sm" className="text-sm">
                <Calendar className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <div className="space-y-4">
              {upcomingClasses.map((classItem, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                  <div>
                    <p className="font-medium text-gray-900">{classItem.group}</p>
                    <p className="text-sm text-gray-600">{classItem.teacher}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-blue-600">{classItem.time}</p>
                    <p className="text-sm text-gray-500">{classItem.room}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Activity Feed */}
        <Card className="dashboard-card">
          <CardHeader className="dashboard-card-header">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Recent Activity</CardTitle>
                <CardDescription className="mt-1">Latest system activities</CardDescription>
              </div>
              <Button variant="ghost" size="sm" className="text-sm">
                <Clock className="h-4 w-4 mr-2" />
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent className="dashboard-card-content">
            <ActivityFeed
              limit={8}
              showHeader={false}
              showRefresh={false}
              showViewAll={false}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

const recentLeads = [
  { name: 'Aziza Karimova', course: 'IELTS Preparation', time: '2 hours ago', status: 'NEW' },
  { name: 'Bobur Toshev', course: 'General English', time: '4 hours ago', status: 'CONTACTED' },
  { name: 'Dilnoza Rahimova', course: 'Kids English', time: '6 hours ago', status: 'INTERESTED' },
  { name: 'Eldor Nazarov', course: 'SAT Preparation', time: '1 day ago', status: 'NEW' },
]

const upcomingClasses = [
  { group: 'IELTS 6.5 - Group A', teacher: 'Ms. Sarah', time: '10:00 AM', room: 'Room 101' },
  { group: 'General B1 - Group C', teacher: 'Mr. John', time: '11:30 AM', room: 'Room 203' },
  { group: 'Kids English - Beginners', teacher: 'Ms. Anna', time: '2:00 PM', room: 'Room 105' },
  { group: 'SAT Math', teacher: 'Mr. David', time: '4:00 PM', room: 'Room 301' },
]
