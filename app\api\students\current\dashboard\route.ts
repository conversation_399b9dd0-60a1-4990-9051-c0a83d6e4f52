import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Find the student profile for the current user
    const student = await prisma.student.findUnique({
      where: { userId: session.user.id },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            phone: true
          }
        },
        enrollments: {
          where: { status: 'ACTIVE' },
          include: {
            group: {
              include: {
                course: {
                  select: {
                    name: true,
                    level: true
                  }
                },
                teacher: {
                  include: {
                    user: {
                      select: {
                        name: true
                      }
                    }
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 1
        },
        payments: {
          select: {
            amount: true,
            status: true,
            dueDate: true,
            paidDate: true
          },
          orderBy: { createdAt: 'desc' }
        },
        attendances: {
          include: {
            class: {
              select: {
                topic: true,
                date: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        assessments: {
          where: { status: 'COMPLETED' },
          select: {
            score: true,
            maxScore: true,
            type: true,
            completedAt: true
          },
          orderBy: { completedAt: 'desc' },
          take: 5
        }
      }
    })

    if (!student) {
      return NextResponse.json({ error: 'Student profile not found' }, { status: 404 })
    }

    // Calculate attendance statistics
    const totalClasses = student.attendances.length
    const attendedClasses = student.attendances.filter(a => a.status === 'PRESENT').length
    const attendanceRate = totalClasses > 0 ? Math.round((attendedClasses / totalClasses) * 100) : 0

    // Calculate payment statistics
    const totalPayments = student.payments.reduce((sum, payment) => sum + Number(payment.amount), 0)
    const paidAmount = student.payments
      .filter(payment => payment.status === 'PAID')
      .reduce((sum, payment) => sum + Number(payment.amount), 0)
    const pendingAmount = totalPayments - paidAmount

    // Get upcoming classes (mock for now - would need class schedule implementation)
    const upcomingClasses = 3 // This would come from actual class schedule

    // Calculate progress (mock calculation based on assessments)
    const averageScore = student.assessments.length > 0
      ? student.assessments.reduce((sum, assessment) => {
          const percentage = assessment.maxScore ? (assessment.score / assessment.maxScore) * 100 : 0
          return sum + percentage
        }, 0) / student.assessments.length
      : 0

    // Determine next level based on current level
    const levelProgression = {
      'A1': 'A2',
      'A2': 'B1',
      'B1': 'B2',
      'B2': 'IELTS',
      'IELTS': 'Advanced',
      'SAT': 'SAT Advanced',
      'MATH': 'Advanced Math',
      'KIDS': 'Kids Advanced'
    }

    const nextLevel = levelProgression[student.level as keyof typeof levelProgression] || 'Advanced'

    // Format recent classes
    const recentClasses = student.attendances.slice(0, 4).map(attendance => ({
      date: attendance.class.date.toISOString().split('T')[0],
      topic: attendance.class.topic || 'Class Session',
      status: attendance.status.toLowerCase()
    }))

    // Calculate pending assignments (mock for now)
    const pendingAssignments = 2 // This would come from actual assignment system

    const dashboardData = {
      student: {
        name: student.user.name,
        level: student.level,
        nextLevel,
        branch: student.branch
      },
      progress: {
        overall: Math.round(averageScore),
        attendance: attendanceRate,
        averageScore: Math.round(averageScore)
      },
      stats: {
        totalClasses,
        attendedClasses,
        upcomingClasses,
        pendingAssignments
      },
      payments: {
        totalPayments,
        paidAmount,
        pendingAmount
      },
      recentClasses,
      currentEnrollment: student.enrollments[0] ? {
        groupName: student.enrollments[0].group.name,
        courseName: student.enrollments[0].group.course.name,
        teacherName: student.enrollments[0].group.teacher?.user.name || 'No Teacher Assigned'
      } : null
    }

    return NextResponse.json(dashboardData)
  } catch (error) {
    console.error('Error fetching student dashboard data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
