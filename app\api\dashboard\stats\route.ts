import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get current date ranges
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)
    const startOfWeek = new Date(now)
    startOfWeek.setDate(now.getDate() - 7)

    // Get total students count
    const totalStudents = await prisma.student.count({
      where: { status: { in: ['ACTIVE', 'PAUSED'] } }
    })

    // Get last month's student count for comparison
    const lastMonthStudents = await prisma.student.count({
      where: {
        status: { in: ['ACTIVE', 'PAUSED'] },
        createdAt: { lte: endOfLastMonth }
      }
    })

    // Calculate student growth percentage
    const studentGrowth = lastMonthStudents > 0 
      ? Math.round(((totalStudents - lastMonthStudents) / lastMonthStudents) * 100)
      : 0

    // Get new leads this week
    const newLeads = await prisma.lead.count({
      where: {
        createdAt: { gte: startOfWeek },
        status: { not: 'ARCHIVED' }
      }
    })

    // Get last week's leads for comparison
    const lastWeekStart = new Date(startOfWeek)
    lastWeekStart.setDate(lastWeekStart.getDate() - 7)
    const lastWeekLeads = await prisma.lead.count({
      where: {
        createdAt: { 
          gte: lastWeekStart,
          lt: startOfWeek
        },
        status: { not: 'ARCHIVED' }
      }
    })

    // Calculate leads growth percentage
    const leadsGrowth = lastWeekLeads > 0 
      ? Math.round(((newLeads - lastWeekLeads) / lastWeekLeads) * 100)
      : 0

    // Get active groups count
    const activeGroups = await prisma.group.count({
      where: { isActive: true }
    })

    // Get monthly revenue
    const monthlyRevenue = await prisma.payment.aggregate({
      where: {
        status: 'PAID',
        paidDate: {
          gte: startOfMonth,
          lte: now
        }
      },
      _sum: { amount: true }
    })

    // Get last month's revenue for comparison
    const lastMonthRevenue = await prisma.payment.aggregate({
      where: {
        status: 'PAID',
        paidDate: {
          gte: startOfLastMonth,
          lte: endOfLastMonth
        }
      },
      _sum: { amount: true }
    })

    // Calculate revenue growth percentage
    const currentRevenue = Number(monthlyRevenue._sum.amount) || 0
    const previousRevenue = Number(lastMonthRevenue._sum.amount) || 0
    const revenueGrowth = previousRevenue > 0 
      ? Math.round(((currentRevenue - previousRevenue) / previousRevenue) * 100)
      : 0

    // Get recent leads (last 4)
    const recentLeads = await prisma.lead.findMany({
      where: { status: { not: 'ARCHIVED' } },
      orderBy: { createdAt: 'desc' },
      take: 4,
      select: {
        id: true,
        name: true,
        coursePreference: true,
        status: true,
        createdAt: true
      }
    })

    // Get upcoming classes for today
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    const upcomingClasses = await prisma.class.findMany({
      where: {
        date: {
          gte: today,
          lt: tomorrow
        }
      },
      include: {
        group: {
          include: {
            course: { select: { name: true, level: true } },
            teacher: { include: { user: { select: { name: true } } } }
          }
        },
        cabinet: { select: { name: true } }
      },
      orderBy: { startTime: 'asc' },
      take: 4
    })

    // Format the response
    const stats = {
      totalStudents: {
        count: totalStudents,
        growth: studentGrowth
      },
      newLeads: {
        count: newLeads,
        growth: leadsGrowth
      },
      activeGroups: {
        count: activeGroups
      },
      monthlyRevenue: {
        amount: currentRevenue,
        growth: revenueGrowth
      },
      recentLeads: recentLeads.map(lead => ({
        name: lead.name,
        course: lead.coursePreference,
        status: lead.status,
        time: getTimeAgo(lead.createdAt)
      })),
      upcomingClasses: upcomingClasses.map(classItem => ({
        group: `${classItem.group.course.name} - ${classItem.group.course.level}`,
        teacher: classItem.group.teacher?.user.name || 'No Teacher',
        time: formatTime(classItem.startTime),
        room: classItem.cabinet?.name || 'TBA'
      }))
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function getTimeAgo(date: Date): string {
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'Just now'
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
  } else {
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
  }
}

function formatTime(timeString: string): string {
  const time = new Date(`1970-01-01T${timeString}`)
  return time.toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit',
    hour12: true 
  })
}
