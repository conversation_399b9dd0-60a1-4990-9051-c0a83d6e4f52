import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import * as z from 'zod'

const announcementSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  targetAudience: z.enum(['STUDENTS', 'TEACHERS', 'PARENTS', 'ALL']).default('ALL'),
  isActive: z.boolean().default(true),
  expiresAt: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const isActive = searchParams.get('isActive')
    const targetAudience = searchParams.get('targetAudience')

    const where: any = {}

    if (isActive !== null) {
      where.isActive = isActive === 'true'
    }

    if (targetAudience) {
      where.targetAudience = targetAudience
    }

    const skip = (page - 1) * limit

    // For now, return empty array since we don't have Announcement model in schema
    // This will be updated when Announcement model is added to the database
    const announcements = []
    const total = 0

    return NextResponse.json({
      announcements,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching announcements:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has permission to create announcements
    if (!['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = announcementSchema.parse(body)

    // For now, just log the announcement creation since we don't have Announcement model
    await ActivityLogger.log({
      userId: session.user.id,
      action: 'CREATE',
      resource: 'ANNOUNCEMENT',
      resourceId: 'temp-id',
      details: `Created announcement: ${validatedData.title}`,
    })

    // Return mock response for now
    const announcement = {
      id: `ann-${Date.now()}`,
      title: validatedData.title,
      content: validatedData.content,
      priority: validatedData.priority,
      targetAudience: validatedData.targetAudience,
      isActive: validatedData.isActive,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      author: { name: session.user.name },
    }

    return NextResponse.json(announcement, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating announcement:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
