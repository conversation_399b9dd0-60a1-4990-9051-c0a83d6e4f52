'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  TrendingUp,
  Award,
  Target,
  BookOpen,
  CheckCircle,
  Clock,
  Star,
  Loader2
} from 'lucide-react'

export default function StudentProgressPage() {
  const [progressData, setProgressData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchProgressData()
  }, [])

  const fetchProgressData = async () => {
    try {
      setLoading(true)
      // For now, we'll use the current user's ID from session
      const response = await fetch('/api/students/current/progress')

      if (!response.ok) {
        throw new Error('Failed to fetch progress data')
      }

      const data = await response.json()
      setProgressData(data)
    } catch (error) {
      console.error('Error fetching progress data:', error)
      setError('Failed to load progress data')
      // Fallback to mock data for now
      setProgressData({
        student: {
          name: 'Student',
          level: 'B1',
          nextLevel: 'B2',
          branch: 'Main Branch'
        },
        progress: {
          overall: 65,
          attendance: 85,
          averageScore: 78
        },
        skills: [
          { name: "Speaking", progress: 70, level: "B1+" },
          { name: "Listening", progress: 65, level: "B1" },
          { name: "Reading", progress: 75, level: "B1+" },
          { name: "Writing", progress: 55, level: "B1-" },
          { name: "Grammar", progress: 80, level: "B2-" },
          { name: "Vocabulary", progress: 60, level: "B1" }
        ],
        achievements: [
          { name: "Perfect Attendance", date: "2024-01-10", icon: "🎯" },
          { name: "Grammar Master", date: "2024-01-05", icon: "📚" },
          { name: "Speaking Champion", date: "2023-12-20", icon: "🗣️" }
        ],
        recentActivity: {
          assessments: [
            { testName: "Unit 5 Test", score: 85, maxScore: 100, completedAt: "2024-01-12" },
            { testName: "Speaking Assessment", score: 78, maxScore: 100, completedAt: "2024-01-08" },
            { testName: "Vocabulary Quiz", score: 92, maxScore: 100, completedAt: "2024-01-05" }
          ]
        }
      })
    } finally {
      setLoading(false)
    }
  }

  const getSkillColor = (progress: number) => {
    if (progress >= 80) return "bg-green-500"
    if (progress >= 60) return "bg-yellow-500"
    return "bg-red-500"
  }

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100
    if (percentage >= 80) return "text-green-600"
    if (percentage >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading progress data...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!progressData) {
    return (
      <div className="space-y-6">
        <Alert>
          <AlertDescription>No progress data available.</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">My Progress</h1>
        <p className="text-gray-600">Track your learning journey and achievements</p>
      </div>

      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Level Progress
          </CardTitle>
          <CardDescription>Your progress from {progressData.student?.level} to {progressData.student?.nextLevel}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Badge className="bg-yellow-100 text-yellow-800">
                  Current: {progressData.student?.level}
                </Badge>
                <span className="text-gray-400">→</span>
                <Badge className="bg-blue-100 text-blue-800">
                  Target: {progressData.student?.nextLevel}
                </Badge>
              </div>
              <span className="text-2xl font-bold">{progressData.progress?.overall}%</span>
            </div>
            <Progress value={progressData.progress?.overall} className="h-3" />
            <p className="text-sm text-gray-600">
              You&apos;re {100 - (progressData.progress?.overall || 0)}% away from reaching {progressData.student?.nextLevel} level!
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Skills Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BookOpen className="h-5 w-5 mr-2" />
            Skills Assessment
          </CardTitle>
          <CardDescription>Your performance across different language skills</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {progressData.skills.map((skill, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{skill.name}</span>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {skill.level}
                    </Badge>
                    <span className="text-sm font-medium">{skill.progress}%</span>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${getSkillColor(skill.progress)}`}
                    style={{ width: `${skill.progress}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Test Results */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2" />
            Recent Test Results
          </CardTitle>
          <CardDescription>Your latest assessment scores</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {progressData.recentActivity?.assessments?.map((test: any, index: number) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">{test.testName}</h4>
                  <p className="text-sm text-gray-500">{new Date(test.completedAt).toLocaleDateString()}</p>
                </div>
                <div className="text-right">
                  <div className={`text-lg font-bold ${getScoreColor(test.score, test.maxScore)}`}>
                    {test.score}/{test.maxScore}
                  </div>
                  <div className="text-sm text-gray-500">
                    {Math.round((test.score / test.maxScore) * 100)}%
                  </div>
                </div>
              </div>
            )) || (
              <p className="text-gray-500 text-center py-4">No recent assessments available.</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Achievements */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Award className="h-5 w-5 mr-2" />
            Achievements
          </CardTitle>
          <CardDescription>Your learning milestones and accomplishments</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {progressData.achievements.map((achievement, index) => (
              <div key={index} className="flex items-center space-x-3 p-4 border rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50">
                <div className="text-2xl">{achievement.icon}</div>
                <div>
                  <h4 className="font-medium">{achievement.name}</h4>
                  <p className="text-sm text-gray-500">{achievement.date}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Learning Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Star className="h-5 w-5 mr-2" />
            Personalized Learning Tips
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="p-3 bg-blue-50 border-l-4 border-blue-400 rounded">
              <p className="text-sm">
                <strong>Focus on Writing:</strong> Your writing skills need improvement. Practice daily writing exercises to reach B1+ level.
              </p>
            </div>
            <div className="p-3 bg-green-50 border-l-4 border-green-400 rounded">
              <p className="text-sm">
                <strong>Great Grammar Progress:</strong> You&apos;re excelling in grammar! Keep up the excellent work.
              </p>
            </div>
            <div className="p-3 bg-yellow-50 border-l-4 border-yellow-400 rounded">
              <p className="text-sm">
                <strong>Vocabulary Building:</strong> Expand your vocabulary by reading more English texts and using new words in conversations.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
